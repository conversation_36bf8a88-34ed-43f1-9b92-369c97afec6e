import React, { useLayoutEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { gsap } from 'gsap';

// Interface pour les méthodes exposées du composant
export interface SunriseAnimationRef {
  triggerSunrise: () => void;
  resetSun: () => void;
}

// Interface pour les props du composant
interface SunriseAnimationProps {
  isVisible: boolean; // Contrôle la visibilité du composant
}

const SunriseAnimation = forwardRef<SunriseAnimationRef, SunriseAnimationProps>(
  ({ isVisible }, ref) => {
    // Références pour les éléments DOM
    const containerRef = useRef<HTMLDivElement>(null);
    const sunWrapperRef = useRef<HTMLDivElement>(null);
    const sunHaloRef = useRef<HTMLDivElement>(null);
    const lensFlareRef = useRef<HTMLDivElement>(null);
    const sunImageRef = useRef<HTMLImageElement>(null);

    // Référence pour la timeline GSAP
    const timelineRef = useRef<gsap.core.Timeline | null>(null);

    // 🌅 FONCTION PRINCIPALE: Déclencher l'animation de lever de soleil
    const triggerSunrise = () => {
      if (!sunWrapperRef.current || !sunHaloRef.current || !lensFlareRef.current) {
        console.warn('🌅 Éléments DOM non prêts pour l\'animation');
        return;
      }

      console.log('🌅 Déclenchement de l\'animation de lever de soleil');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation de lever de soleil terminée');
        }
      });

      // 🔧 CISCO: Position initiale - soleil visible mais bas
      gsap.set(sunWrapperRef.current, {
        y: '80%', // Plus haut pour être visible
        opacity: 1
      });
      gsap.set(sunHaloRef.current, {
        opacity: 0,
        scale: 0.3
      });
      gsap.set(lensFlareRef.current, {
        opacity: 0,
        scale: 0.2,
        y: '50%'
      });

      // 🌅 PHASE 1: Le soleil se lève progressivement (16 secondes = 2x durée background)
      timelineRef.current.fromTo(
        sunWrapperRef.current,
        { y: '80%' },
        {
          y: '40%', // Plus haut pour être bien visible
          duration: 16.0,
          ease: 'power2.out'
        }
      );

      // 🌟 PHASE 2: Le halo apparaît et s'intensifie (commence après 2 secondes)
      timelineRef.current.fromTo(
        sunHaloRef.current,
        { opacity: 0, scale: 0.3 },
        { 
          opacity: 0.8, // Opacité réduite pour effet plus subtil
          scale: 1.2, // Légèrement plus grand pour effet magistral
          duration: 12.0,
          ease: 'power2.inOut'
        },
        2 // Démarre après 2 secondes
      );

      // ✨ PHASE 3: Le lens flare apparaît avec décalage (commence après 4 secondes)
      timelineRef.current.fromTo(
        lensFlareRef.current,
        { opacity: 0, y: '50%', scale: 0.2 },
        { 
          opacity: 0.6, // Opacité modérée pour effet réaliste
          y: '0%', 
          scale: 1,
          duration: 10.0,
          ease: 'power2.out'
        },
        4 // Démarre après 4 secondes
      );
    };

    // 🔄 FONCTION: Remettre le soleil en position initiale
    const resetSun = () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      if (sunWrapperRef.current && sunHaloRef.current && lensFlareRef.current) {
        gsap.set([sunWrapperRef.current, sunHaloRef.current, lensFlareRef.current], {
          y: '80%',
          opacity: 0,
          scale: 0.3
        });
      }

      console.log('🔄 Soleil remis en position initiale');
    };

    // Exposer les méthodes via useImperativeHandle
    useImperativeHandle(ref, () => ({
      triggerSunrise,
      resetSun
    }));

    // Cleanup à la destruction du composant
    useLayoutEffect(() => {
      return () => {
        if (timelineRef.current) {
          timelineRef.current.kill();
        }
      };
    }, []);

    // Ne pas rendre si non visible
    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={containerRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 6 }} // 🔧 CISCO: Devant le paysage (5) pour être visible au-dessus de l'horizon
      >
        {/* Conteneur pour le soleil et ses effets */}
        <div 
          ref={sunWrapperRef} 
          className="absolute w-32 h-32 left-1/2 top-1/2 -translate-x-1/2"
          style={{
            transform: 'translateX(-50%) translateY(80%)', // Position initiale visible
          }}
        >
          <div className="relative w-full h-full">
            {/* EFFET 1: Le Halo lumineux */}
            <div 
              ref={sunHaloRef} 
              className="sun-halo absolute inset-0 opacity-0"
            />
            
            {/* EFFET 2: Le Lens Flare */}
            <div 
              ref={lensFlareRef} 
              className="lens-flare absolute inset-[-200%] opacity-0"
            />

            {/* L'image du soleil */}
            <img 
              ref={sunImageRef}
              src="/SUN.png" 
              alt="Soleil" 
              className="relative z-10 w-full h-full object-contain"
              style={{
                filter: 'drop-shadow(0 0 20px rgba(255, 220, 0, 0.6))'
              }}
            />
          </div>
        </div>
      </div>
    );
  }
);

SunriseAnimation.displayName = 'SunriseAnimation';

export default SunriseAnimation;
